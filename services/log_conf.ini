[loggers]
keys=root,app,uvicorn

[handlers]
keys=console

[formatters]
keys=console

[logger_root]
level=DEBUG
handlers=console

[logger_app]
level=DEBUG
handlers=console
propagate=1
qualname=app

[logger_uvicorn]
level=DEBUG
handlers=console
propagate=1
qualname=uvicorn

[handler_console]
class=StreamHandler
level=DEBUG
formatter=console
args=(sys.stdout,)

[formatter_console]
format=%(asctime)s - %(name)s - %(levelname)s [%(thread)d] - %(funcName)s:%(lineno)d - %(message)s