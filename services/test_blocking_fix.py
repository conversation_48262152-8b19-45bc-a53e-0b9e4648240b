import pytest
import asyncio
import unittest.mock as mock
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock external dependencies before importing main
with patch('google.cloud.storage.Client'), \
     patch('mediapipe.tasks.python.vision.PoseLandmarker.create_from_options'), \
     patch('cv2.imread'), \
     patch('pika.BlockingConnection'), \
     patch('logging.config.fileConfig'):
    
    # Mock environment variables
    os.environ.update({
        'GCS_BUCKET_NAME': 'test-bucket',
        'MP_MIN_POSE_PRESENCE_CONFIDENCE': '0.5',
        'MP_MIN_TRACKING_CONFIDENCE': '0.5',
        'MP_MIN_POSE_DETECTION_CONFIDENCE': '0.5',
        'RABBITMQ_HOST': 'localhost',
        'RABBITMQ_PORT': '5672',
        'RABBITMQ_USER': 'test',
        'RABBITMQ_PASSWORD': 'test',
        'RABBITMQ_QUEUE_NAME': 'test-queue',
        'RABBITMQ_EXCHANGE_NAME': 'test-exchange',
        'RABBITMQ_EXCHANGE_TYPE': 'direct',
        'LIVEKIT_SERVER_URL': 'ws://localhost:7880',
        'LIVEKIT_API_KEY': 'test-key',
        'LIVEKIT_API_SECRET': 'test-secret'
    })
    
    from main import app_state, handle_stop_recording


@pytest.mark.asyncio
async def test_blocking_fix_with_short_timeout():
    """Test that the blocking while loop was replaced with async waiting - SHORT VERSION"""
    # Reset app_state
    app_state.video_writers = {"track_1": MagicMock()}
    app_state.video_origin_writers = {"track_1": MagicMock()}
    app_state.video_filenames = {"track_1": "test.mp4"}
    app_state.video_origin_filenames = {"track_1": "test_origin.mp4"}
    app_state.pending_task = {"track_1": 5}  # Will cause a short wait
    
    mock_room = MagicMock()
    mock_room.local_participant.publish_data = AsyncMock()
    
    with patch('main.bucket') as mock_bucket, \
         patch('os.remove'):
        
        mock_bucket.blob.return_value = MagicMock()
        
        # Simulate pending tasks clearing after 0.5 seconds
        async def clear_pending_after_delay():
            await asyncio.sleep(0.5)
            app_state.pending_task["track_1"] = 0
        
        start_time = asyncio.get_event_loop().time()
        
        # Run both tasks concurrently
        clear_task = asyncio.create_task(clear_pending_after_delay())
        stop_task = asyncio.create_task(handle_stop_recording(mock_room))
        
        # Wait for both to complete
        result = await stop_task
        await clear_task
        
        end_time = asyncio.get_event_loop().time()
        
        # Should complete in about 0.5 seconds (when pending tasks clear)
        # This proves it's using async waiting, not blocking
        assert end_time - start_time < 1.0  # Should be much faster than 30s timeout
        assert end_time - start_time >= 0.4  # But should wait for the pending tasks
        assert result["status"] == "Recording stopped"
        
        print(f"✅ CRITICAL FIX VERIFIED: Async waiting took {end_time - start_time:.2f}s instead of blocking!")


@pytest.mark.asyncio 
async def test_concurrent_operations_dont_block():
    """Test that multiple async operations can run concurrently without blocking"""
    
    async def dummy_async_operation(duration, name):
        """Simulate an async operation"""
        await asyncio.sleep(duration)
        return f"Operation {name} completed"
    
    # Start multiple operations concurrently
    start_time = asyncio.get_event_loop().time()
    
    tasks = [
        asyncio.create_task(dummy_async_operation(0.1, "A")),
        asyncio.create_task(dummy_async_operation(0.1, "B")),
        asyncio.create_task(dummy_async_operation(0.1, "C"))
    ]
    
    results = await asyncio.gather(*tasks)
    
    end_time = asyncio.get_event_loop().time()
    
    # All operations should complete in about 0.1s (concurrent), not 0.3s (sequential)
    assert end_time - start_time < 0.2
    assert len(results) == 3
    
    print(f"✅ CONCURRENCY VERIFIED: 3 operations completed in {end_time - start_time:.2f}s concurrently!")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
