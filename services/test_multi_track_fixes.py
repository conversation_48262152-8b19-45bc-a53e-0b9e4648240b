import pytest
import asyncio
import unittest.mock as mock
import numpy as np
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock external dependencies before importing main
with patch('google.cloud.storage.Client'), \
     patch('mediapipe.tasks.python.vision.PoseLandmarker.create_from_options'), \
     patch('cv2.imread'), \
     patch('pika.BlockingConnection'), \
     patch('logging.config.fileConfig'):

    # Mock environment variables
    os.environ.update({
        'GCS_BUCKET_NAME': 'test-bucket',
        'MP_MIN_POSE_PRESENCE_CONFIDENCE': '0.5',
        'MP_MIN_TRACKING_CONFIDENCE': '0.5',
        'MP_MIN_POSE_DETECTION_CONFIDENCE': '0.5',
        'RABBITMQ_HOST': 'localhost',
        'RABBITMQ_PORT': '5672',
        'RABBITMQ_USER': 'test',
        'RABBITMQ_PASSWORD': 'test',
        'RABBITMQ_QUEUE_NAME': 'test-queue',
        'RABBITMQ_EXCHANGE_NAME': 'test-exchange',
        'RABBITMQ_EXCHANGE_TYPE': 'direct',
        'LIVEKIT_SERVER_URL': 'ws://localhost:7880',
        'LIVEKIT_API_KEY': 'test-key',
        'LIVEKIT_API_SECRET': 'test-secret'
    })

    from main import ApplicationState, app_state, handle_start_recording, handle_stop_recording, process_and_publish


class TestMultiTrackFixes:
    """Test fixes for multi-track concurrency issues"""

    def test_thread_safe_pending_operations(self):
        """Test that pending task operations are thread-safe"""
        state = ApplicationState()

        # Test that we have thread-safe methods
        assert hasattr(state, 'safe_increment_pending')
        assert hasattr(state, 'safe_decrement_pending')
        assert hasattr(state, 'safe_get_pending')
        assert hasattr(state, 'safe_reset_pending')
        assert hasattr(state, '_lock')

        print("✅ Thread-safe pending task methods available")

    @pytest.mark.asyncio
    async def test_concurrent_pending_task_operations(self):
        """Test concurrent pending task operations don't cause race conditions"""
        state = ApplicationState()
        track_ids = ["track_1", "track_2", "track_3"]

        async def increment_task(track_id, iterations):
            for _ in range(iterations):
                await state.safe_increment_pending(track_id)
                await asyncio.sleep(0.001)  # Small delay to simulate real work

        async def decrement_task(track_id, iterations):
            await asyncio.sleep(0.01)  # Start slightly later
            for _ in range(iterations):
                await state.safe_decrement_pending(track_id)
                await asyncio.sleep(0.001)

        # Run concurrent operations
        tasks = []
        iterations = 10

        for track_id in track_ids:
            tasks.append(asyncio.create_task(increment_task(track_id, iterations)))
            tasks.append(asyncio.create_task(decrement_task(track_id, iterations)))

        await asyncio.gather(*tasks)

        # Verify final state is consistent
        for track_id in track_ids:
            final_count = await state.safe_get_pending(track_id)
            assert final_count == 0, f"Track {track_id} has non-zero pending count: {final_count}"

        print("✅ Concurrent pending task operations work correctly")

    @pytest.mark.asyncio
    async def test_multi_track_stop_recording_race_condition(self):
        """Test that stop recording handles multiple tracks without race conditions"""
        # Set up multiple tracks
        track_ids = ["track_1", "track_2", "track_3"]

        # Reset app_state
        app_state.video_writers = {}
        app_state.video_origin_writers = {}
        app_state.video_filenames = {}
        app_state.video_origin_filenames = {}
        app_state.pending_task.clear()

        # Set up mock writers for multiple tracks
        for track_id in track_ids:
            mock_writer = MagicMock()
            mock_origin_writer = MagicMock()

            app_state.video_writers[track_id] = mock_writer
            app_state.video_origin_writers[track_id] = mock_origin_writer
            app_state.video_filenames[track_id] = f"{track_id}_test.mp4"
            app_state.video_origin_filenames[track_id] = f"{track_id}_test_origin.mp4"
            app_state.pending_task[track_id] = 2  # Simulate pending tasks

        mock_room = MagicMock()
        mock_room.local_participant.publish_data = AsyncMock()

        # Simulate pending tasks clearing for different tracks at different times
        async def clear_pending_tasks():
            await asyncio.sleep(0.1)
            await app_state.safe_reset_pending("track_1")
            await asyncio.sleep(0.1)
            await app_state.safe_reset_pending("track_2")
            await asyncio.sleep(0.1)
            await app_state.safe_reset_pending("track_3")

        # Store references to mock writers before they get cleared
        mock_writers = {}
        mock_origin_writers = {}
        for track_id in track_ids:
            mock_writers[track_id] = app_state.video_writers[track_id]
            mock_origin_writers[track_id] = app_state.video_origin_writers[track_id]

        with patch('main.bucket') as mock_bucket, \
             patch('os.remove'), \
             patch('os.path.exists', return_value=True):

            mock_bucket.blob.return_value = MagicMock()

            # Run both tasks concurrently
            clear_task = asyncio.create_task(clear_pending_tasks())
            stop_task = asyncio.create_task(handle_stop_recording(mock_room))

            result = await stop_task
            await clear_task

            # Verify all writers were released (using stored references)
            for track_id in track_ids:
                mock_writers[track_id].release.assert_called_once()
                mock_origin_writers[track_id].release.assert_called_once()

            # Verify state was cleared properly
            assert len(app_state.video_writers) == 0
            assert len(app_state.video_origin_writers) == 0
            assert result["status"] == "Recording stopped"
            assert app_state.is_recording == False

        print("✅ Multi-track stop recording handles race conditions correctly")

    @pytest.mark.asyncio
    async def test_concurrent_frame_processing(self):
        """Test that multiple tracks can process frames concurrently without conflicts"""

        # Create mock tracks
        tracks = []
        video_streams = []

        for i in range(3):
            track = MagicMock()
            track._info.sid = f"track_{i+1}"
            video_stream = MagicMock()

            # Create async iterator for frames
            async def frame_generator(track_id):
                for frame_num in range(5):
                    frame_event = MagicMock()
                    frame_event.frame.data = np.random.bytes(640 * 480 * 3)
                    frame_event.frame.height = 480
                    frame_event.frame.width = 640
                    yield frame_event
                    await asyncio.sleep(0.01)  # Simulate frame timing

            video_stream.__aiter__ = lambda vs=video_stream, tid=track._info.sid: frame_generator(tid)

            tracks.append(track)
            video_streams.append(video_stream)

        # Set up app_state for recording
        app_state.is_recording = True
        app_state.video_writers = {}
        app_state.video_origin_writers = {}

        for track in tracks:
            app_state.video_writers[track._info.sid] = MagicMock()
            app_state.video_origin_writers[track._info.sid] = MagicMock()

        with patch('main.process_frame_with_pose') as mock_process:
            mock_process.return_value = np.zeros((480, 640, 3), dtype=np.uint8)

            # Start processing all tracks concurrently
            tasks = []
            for track, video_stream in zip(tracks, video_streams):
                task = asyncio.create_task(process_and_publish(track, video_stream))
                tasks.append(task)

            # Let them run for a short time
            await asyncio.sleep(0.2)

            # Cancel all tasks
            for task in tasks:
                task.cancel()

            # Wait for cancellation
            await asyncio.gather(*tasks, return_exceptions=True)

            # Verify no pending tasks are left in inconsistent state
            for track in tracks:
                pending = await app_state.safe_get_pending(track._info.sid)
                assert pending >= 0, f"Negative pending count for {track._info.sid}: {pending}"

        print("✅ Concurrent frame processing works without conflicts")

    @pytest.mark.asyncio
    async def test_track_cleanup_thread_safety(self):
        """Test that track cleanup is thread-safe"""
        state = ApplicationState()
        track_id = "test_track"

        # Set up track resources
        mock_writer = MagicMock()
        mock_origin_writer = MagicMock()

        state.video_writers[track_id] = mock_writer
        state.video_origin_writers[track_id] = mock_origin_writer
        state.video_filenames[track_id] = "test.mp4"
        state.video_origin_filenames[track_id] = "test_origin.mp4"
        state.pending_task[track_id] = 5

        # Clean up
        await state.cleanup_track_resources(track_id)

        # Verify cleanup
        assert track_id not in state.video_writers
        assert track_id not in state.video_origin_writers
        assert track_id not in state.video_filenames
        assert track_id not in state.video_origin_filenames
        assert track_id not in state.pending_task

        # Verify writers were released
        mock_writer.release.assert_called_once()
        mock_origin_writer.release.assert_called_once()

        print("✅ Track cleanup is thread-safe")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
