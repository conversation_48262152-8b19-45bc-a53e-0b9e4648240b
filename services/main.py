import logging
import logging.config
import asyncio
import os
import uuid
import threading
import psutil
from aiohttp import ClientTimeout
from livekit import api, rtc
from signal import SIGINT, SIGTERM
import json
import pika
import cv2
import mediapipe as mp
from mediapipe.tasks import python
from mediapipe.tasks.python import vision
from mediapipe.framework.formats import landmark_pb2
from mediapipe import solutions
import numpy as np
from google.cloud import storage
from datetime import datetime
import time
import gc
from collections import defaultdict
from threading import Lock
import ffmpeg
import tempfile


# Disable TensorFlow logging
# import os
# os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=all, 1=no INFO, 2=no INFO/WARN, 3=no INFO/WARN/ERROR

logging.config.fileConfig("log_conf.ini")
LOGGER = logging.getLogger(__name__)

# Use a class to manage state instead of global variables
class ApplicationState:
    def __init__(self):
        self.tasks = {}
        self.is_recording = False
        self.video_writers = {}
        self.video_origin_writers = {}
        self.pending_task = defaultdict(int)  # Thread-safe default dict
        self.video_height = 960
        self.video_width = 720
        self.background_tasks = set()  # Track background tasks
        self._lock = asyncio.Lock()  # Async lock for thread safety
        self.chunk_duration = 30  # 1 minutes for chungk
        self.chunk_start_time = {}
        self.current_chunk = {}
        self.chunk_files = defaultdict(list)  # Track all chunk files for each track
        self.chunk_origin_files = defaultdict(list)  # Track all origin chunk files

    async def safe_increment_pending(self, track_sid):
        """Thread-safe increment of pending task counter"""
        async with self._lock:
            self.pending_task[track_sid] += 1
            return self.pending_task[track_sid]

    async def safe_decrement_pending(self, track_sid):
        """Thread-safe decrement of pending task counter"""
        async with self._lock:
            if track_sid in self.pending_task and self.pending_task[track_sid] > 0:
                self.pending_task[track_sid] -= 1
            return self.pending_task[track_sid]

    async def safe_get_pending(self, track_sid):
        """Thread-safe get pending task count"""
        async with self._lock:
            return self.pending_task.get(track_sid, 0)

    async def safe_reset_pending(self, track_sid):
        """Thread-safe reset of pending task counter"""
        async with self._lock:
            self.pending_task[track_sid] = 0

    def periodic_cleanup(self):
        """Perform periodic cleanup to prevent memory leaks"""

        # Clean up completed background tasks
        completed_tasks = {task for task in self.background_tasks if task.done()}
        self.background_tasks -= completed_tasks

        # Force garbage collection periodically
        gc.collect()

        LOGGER.debug(f"Periodic cleanup: "
                    f"{len(self.background_tasks)} background tasks, "
                    f"{len(self.tasks)} active tracks")

    async def cleanup_track_resources(self, track_sid):
        """Clean up all resources for a specific track - thread safe"""
        async with self._lock:
            self.tasks.pop(track_sid, None)
            if track_sid in self.video_writers:
                writer = self.video_writers.pop(track_sid, None)
                if writer:
                    try:
                        writer.release()
                        del writer  # Explicitly delete the reference
                    except Exception as e:
                        LOGGER.error(f"Error releasing video writer for {track_sid}: {e}")
            if track_sid in self.video_origin_writers:
                writer = self.video_origin_writers.pop(track_sid, None)
                if writer:
                    try:
                        writer.release()
                        del writer  # Explicitly delete the reference
                    except Exception as e:
                        LOGGER.error(f"Error releasing origin writer for {track_sid}: {e}")
            self.pending_task.pop(track_sid, None)

            # Force garbage collection after cleanup
            gc.collect()

    async def rotate_video_writer(self, track_sid):
        """Rotate video writer to a new chunk"""
        async with self._lock:
            if track_sid in self.video_writers and self.video_writers[track_sid] is not None:
                # Close current writers
                try:
                    # Log memory usage
                    process = psutil.Process()
                    memory_info = process.memory_info()
                    LOGGER.info(f"Memory usage before release video_writers - RSS: {memory_info.rss / 1024 / 1024:.2f} MB, VMS: {memory_info.vms / 1024 / 1024:.2f} MB")

                    # Force flush and release video writers
                    try:
                        # Ensure all frames are written to disk before release
                        if hasattr(self.video_writers[track_sid], 'release'):
                            self.video_writers[track_sid].release()
                        del self.video_writers[track_sid]

                        if hasattr(self.video_origin_writers[track_sid], 'release'):
                            self.video_origin_writers[track_sid].release()
                        del self.video_origin_writers[track_sid]
                    except Exception as release_error:
                        LOGGER.error(f"Error during writer release: {release_error}")

                    # Set to None after deletion
                    self.video_writers[track_sid] = None
                    self.video_origin_writers[track_sid] = None

                    # Multiple garbage collection attempts with delays
                    for _ in range(3):
                        gc.collect()
                        await asyncio.sleep(0.1)

                    # Additional memory cleanup attempts
                    import ctypes
                    if hasattr(ctypes, 'windll'):
                        # Windows specific memory cleanup
                        ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1, -1)

                    memory_info = process.memory_info()
                    LOGGER.info(f"Memory usage after release video_writers - RSS: {memory_info.rss / 1024 / 1024:.2f} MB, VMS: {memory_info.vms / 1024 / 1024:.2f} MB")

                except Exception as e:
                    LOGGER.error(f"Error releasing writers for {track_sid}: {e}")

            # Create new filenames with chunk number
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            chunk_num = self.current_chunk.get(track_sid, 0)
            video_filename = f"{track_sid}_{timestamp}_chunk{chunk_num}.mp4"
            video_origin_filename = f"{track_sid}_{timestamp}_chunk{chunk_num}_origin.mp4"

            # Store chunk filenames
            self.chunk_files[track_sid].append(video_filename)
            self.chunk_origin_files[track_sid].append(video_origin_filename)

            # Create new writers
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            self.video_writers[track_sid] = cv2.VideoWriter(
                video_filename,
                fourcc,
                30.0,
                (self.video_width, self.video_height),
                isColor=True
            )
            self.video_origin_writers[track_sid] = cv2.VideoWriter(
                video_origin_filename,
                fourcc,
                30.0,
                (self.video_width, self.video_height),
                isColor=True
            )

            # Update chunk tracking
            self.current_chunk[track_sid] = chunk_num + 1
            self.chunk_start_time[track_sid] = time.time()

    async def merge_chunks(self, track_sid):
        """Merge all chunks for a track into a single video file"""
        try:
            if not self.chunk_files[track_sid]:
                return None

            # Create temporary file for the file list
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                for chunk_file in self.chunk_files[track_sid]:
                    if os.path.exists(chunk_file):
                        f.write(f"file '{os.path.abspath(chunk_file)}'\n")
                file_list_path = f.name

            # Create output filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            merged_filename = f"{track_sid}_{timestamp}.mp4"
            merged_origin_filename = f"{track_sid}_{timestamp}_origin.mp4"

            # Merge processed video chunks
            try:
                stream = ffmpeg.input(file_list_path, format='concat', safe=0)
                stream = ffmpeg.output(stream, merged_filename, c='copy')
                ffmpeg.run(stream, overwrite_output=True, capture_stdout=True, capture_stderr=True)
            except ffmpeg.Error as e:
                LOGGER.error(f"Error merging processed chunks: {e.stderr.decode()}")
                return None

            # Merge origin video chunks
            try:
                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                    for chunk_file in self.chunk_origin_files[track_sid]:
                        if os.path.exists(chunk_file):
                            f.write(f"file '{os.path.abspath(chunk_file)}'\n")
                    origin_file_list_path = f.name

                stream = ffmpeg.input(origin_file_list_path, format='concat', safe=0)
                stream = ffmpeg.output(stream, merged_origin_filename, c='copy')
                ffmpeg.run(stream, overwrite_output=True, capture_stdout=True, capture_stderr=True)
            except ffmpeg.Error as e:
                LOGGER.error(f"Error merging origin chunks: {e.stderr.decode()}")
                return None

            # Clean up temporary files
            os.unlink(file_list_path)
            os.unlink(origin_file_list_path)

            # Clean up chunk files
            for chunk_file in self.chunk_files[track_sid]:
                if os.path.exists(chunk_file):
                    os.remove(chunk_file)
            for chunk_file in self.chunk_origin_files[track_sid]:
                if os.path.exists(chunk_file):
                    os.remove(chunk_file)

            return merged_filename, merged_origin_filename

        except Exception as e:
            LOGGER.error(f"Error merging chunks for {track_sid}: {str(e)}", exc_info=True)
            return None

# Global application state
app_state = ApplicationState()

# Initialize Google Cloud Storage client
storage_client = storage.Client()
bucket_name = os.getenv('GCS_BUCKET_NAME')
bucket = storage_client.bucket(bucket_name)

base_options = python.BaseOptions(model_asset_path='./pose_landmarker_heavy.task')
options = vision.PoseLandmarkerOptions(
    base_options=base_options,
    output_segmentation_masks=False,
    min_pose_presence_confidence = float(os.getenv("MP_MIN_POSE_PRESENCE_CONFIDENCE")),
    min_tracking_confidence = float(os.getenv("MP_MIN_TRACKING_CONFIDENCE")),
    min_pose_detection_confidence = float(os.getenv("MP_MIN_POSE_DETECTION_CONFIDENCE")),
    )
detector = vision.PoseLandmarker.create_from_options(options)

# face_base_options = python.BaseOptions(model_asset_path='./blaze_face_short_range.tflite')
# face_options = vision.FaceDetectorOptions(base_options=face_base_options)
# face_detector = vision.FaceDetector.create_from_options(face_options)

fakeImage = cv2.imread("./fake_image.png")

async def process_frame_with_pose(frame_rgb):
    try:
        # Convert numpy array to MediaPipe Image
        mp_image = mp.Image(image_format=mp.ImageFormat.SRGB, data=frame_rgb)

        # Process the frame
        results = detector.detect(mp_image)
        pose_landmarks_list = results.pose_landmarks
        if pose_landmarks_list is None or len(pose_landmarks_list) == 0:
            return None

        # Draw pose landmarks
        for idx in range(len(pose_landmarks_list)):
            pose_landmarks = pose_landmarks_list[idx]
            # Get ear positions (x,y) from landmarks
            left_ear_x = int(pose_landmarks[7].x * frame_rgb.shape[1])
            left_ear_y = int(pose_landmarks[7].y * frame_rgb.shape[0])
            right_ear_x = int(pose_landmarks[8].x * frame_rgb.shape[1])
            right_ear_y = int(pose_landmarks[8].y * frame_rgb.shape[0])

            # Calculate face region with padding
            face_top = max(0, min(left_ear_y, right_ear_y) - 50)
            face_bottom = min(frame_rgb.shape[0], max(left_ear_y, right_ear_y) + 50)
            face_left = max(0, min(left_ear_x, right_ear_x) - 50)
            face_right = min(frame_rgb.shape[1], max(left_ear_x, right_ear_x) + 50)

            # Get the face region directly (no caching for dancing scenarios)
            face_region = frame_rgb[face_top:face_bottom, face_left:face_right]

            # Only replace if we have a valid region
            if face_region.size > 0:
                # Resize fake image to match face region
                replacing_face = cv2.resize(fakeImage, (face_region.shape[1], face_region.shape[0]))
                # Replace the face region
                frame_rgb[face_top:face_bottom, face_left:face_right] = replacing_face

            # Draw the pose landmarks.
            pose_landmarks_proto = landmark_pb2.NormalizedLandmarkList()
            pose_landmarks_proto.landmark.extend([
            landmark_pb2.NormalizedLandmark(x=landmark.x, y=landmark.y, z=landmark.z) for landmark in pose_landmarks
            ])
            solutions.drawing_utils.draw_landmarks(
            frame_rgb,
            pose_landmarks_proto,
            solutions.pose.POSE_CONNECTIONS,
            solutions.drawing_styles.get_default_pose_landmarks_style())

        return frame_rgb
    except Exception as e:
        LOGGER.error(f"Error in process_frame_with_pose: {str(e)}", exc_info=True)
        return None
    finally:
        # Force garbage collection to clean up MediaPipe resources
        gc.collect()

queue_host = os.getenv('RABBITMQ_HOST')
queue_port = os.getenv('RABBITMQ_PORT')
queue_user = os.getenv('RABBITMQ_USER')
queue_password = os.getenv('RABBITMQ_PASSWORD')
queue_name = os.getenv('RABBITMQ_QUEUE_NAME')
exchange_name = os.getenv('RABBITMQ_EXCHANGE_NAME')
exchange_type = os.getenv('RABBITMQ_EXCHANGE_TYPE')

credentials = pika.PlainCredentials(queue_user, queue_password)
connection = pika.BlockingConnection(pika.ConnectionParameters(host=queue_host,port=queue_port,credentials=credentials))
channel = connection.channel()
channel.basic_qos(prefetch_count=1)
channel.exchange_declare(exchange=exchange_name, exchange_type=exchange_type, passive=True)
queue = channel.queue_declare(queue=queue_name, passive=True)

# Get the message count
message_count = queue.method.message_count

# Check if the queue is empty
if message_count == 0:
    LOGGER.info("Don't have any message in queue. Stop the server.")
    os._exit(0)

class LiveKitConfig:
    url = os.getenv('LIVEKIT_SERVER_URL')
    apiKey = os.getenv('LIVEKIT_API_KEY')
    apiSecret = os.getenv('LIVEKIT_API_SECRET')

def stop_server(queue_channel, queue_method):
    LOGGER.info("Stop streaming EXIT server")
    try:
        # Delete received message from queue
        queue_channel.basic_ack(delivery_tag=queue_method.delivery_tag)
        os._exit(0)
    except Exception as e:
        LOGGER.error(f"Error in stop_server: {str(e)}", exc_info=True)
        os._exit(1)
    finally:
        LOGGER.info("Stop heart_beat_thread due to metadata")

async def handle_start_recording():
    LOGGER.info(f"Starting recording")
    try:
        # Initialize video writers for all active tracks
        for track_sid in app_state.tasks.keys():
            if track_sid not in app_state.video_writers or app_state.video_writers[track_sid] is None:
                await app_state.rotate_video_writer(track_sid)
    except Exception as e:
        LOGGER.error(f"Error in track creation/publishing: {str(e)}", exc_info=True)
        raise
    app_state.is_recording = True
    return {"status": "Recording started"}

async def handle_stop_recording(room: rtc.Room):
    LOGGER.info(f"Stopping recording from")

    # Create date-based folder name
    current_date = datetime.now().strftime("%Y-%m-%d")
    gcs_folder = f"recordings/{current_date}"

    try:
        # Process each track independently to avoid race conditions
        for track_sid in app_state.tasks.keys():
            try:
                # FIXED: Use thread-safe pending task checking
                max_wait_time = 60  # Maximum wait time in seconds
                wait_interval = 0.1  # Check every 100ms
                waited_time = 0

                while True:
                    pending_count = await app_state.safe_get_pending(track_sid)
                    LOGGER.info(f"waiting for finishing writing for {track_sid}")
                    if pending_count == 0 or waited_time >= max_wait_time:
                        break
                    await asyncio.sleep(wait_interval)
                    waited_time += wait_interval

                if waited_time >= max_wait_time:
                    LOGGER.warning(f"Timeout waiting for pending tasks for track {track_sid}")

                # CRITICAL FIX: Create a snapshot of video_writers to avoid modification during iteration
                async with app_state._lock:
                    # Close current writers
                    if track_sid in app_state.video_writers and app_state.video_writers[track_sid] is not None:
                        try:
                            app_state.video_writers[track_sid].release()
                            app_state.video_origin_writers[track_sid].release()
                        except Exception as e:
                            LOGGER.error(f"Error releasing writers for {track_sid}: {e}")

                    # Merge chunks if they exist
                    if app_state.chunk_files[track_sid]:
                        try:
                            # Create temporary file for the file list
                            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                                for chunk_file in app_state.chunk_files[track_sid]:
                                    if os.path.exists(chunk_file):
                                        f.write(f"file '{os.path.abspath(chunk_file)}'\n")
                                file_list_path = f.name

                            # Create output filename
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            merged_filename = f"{track_sid}_{timestamp}.mp4"
                            merged_origin_filename = f"{track_sid}_{timestamp}_origin.mp4"

                            # Merge processed video chunks
                            try:
                                stream = ffmpeg.input(file_list_path, format='concat', safe=0)
                                stream = ffmpeg.output(stream, merged_filename, c='copy')
                                ffmpeg.run(stream, overwrite_output=True, capture_stdout=True, capture_stderr=True)
                            except ffmpeg.Error as e:
                                LOGGER.error(f"Error merging processed chunks: {e.stderr.decode()}")
                                continue

                            # Merge origin video chunks
                            try:
                                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                                    for chunk_file in app_state.chunk_origin_files[track_sid]:
                                        if os.path.exists(chunk_file):
                                            f.write(f"file '{os.path.abspath(chunk_file)}'\n")
                                    origin_file_list_path = f.name

                                stream = ffmpeg.input(origin_file_list_path, format='concat', safe=0)
                                stream = ffmpeg.output(stream, merged_origin_filename, c='copy')
                                ffmpeg.run(stream, overwrite_output=True, capture_stdout=True, capture_stderr=True)
                            except ffmpeg.Error as e:
                                LOGGER.error(f"Error merging origin chunks: {e.stderr.decode()}")
                                continue

                            # Clean up temporary files
                            os.unlink(file_list_path)
                            os.unlink(origin_file_list_path)

                            # Upload merged files to GCS
                            try:
                                blob = bucket.blob(f"{gcs_folder}/anonymized/{os.path.basename(merged_filename)}")
                                blob.upload_from_filename(merged_filename, timeout=86400)
                                blob_origin = bucket.blob(f"{gcs_folder}/origin/{os.path.basename(merged_origin_filename)}")
                                blob_origin.upload_from_filename(merged_origin_filename, timeout=86400)
                                LOGGER.info(f"Done uploaded {merged_filename} and {merged_origin_filename}")

                                # Clean up merged files
                                os.remove(merged_filename)
                                os.remove(merged_origin_filename)
                            except Exception as e:
                                LOGGER.error(f"Error uploading merged files for {track_sid}: {e}")

                            # Clean up chunk files
                            for chunk_file in app_state.chunk_files[track_sid]:
                                if os.path.exists(chunk_file):
                                    os.remove(chunk_file)
                            for chunk_file in app_state.chunk_origin_files[track_sid]:
                                if os.path.exists(chunk_file):
                                    os.remove(chunk_file)

                        except Exception as e:
                            LOGGER.error(f"Error processing chunks for {track_sid}: {str(e)}", exc_info=True)

            except Exception as e:
                LOGGER.error(f"Error processing track {track_sid} during stop recording: {e}", exc_info=True)

        # Clear all recording state safely
        async with app_state._lock:
            app_state.video_writers.clear()
            app_state.video_origin_writers.clear()
            app_state.pending_task.clear()
            app_state.is_recording = False
            app_state.chunk_files.clear()
            app_state.chunk_origin_files.clear()
            app_state.chunk_start_time.clear()
            app_state.current_chunk.clear()

    except Exception as e:
        LOGGER.error(f"Error in handle_stop_recording: {str(e)}", exc_info=True)
        # Ensure recording state is cleared even on error
        async with app_state._lock:
            app_state.is_recording = False
        raise

    await room.local_participant.publish_data(bytes("DONE_UPLOADED", 'utf-8'))
    return {"status": "Recording stopped"}


async def process_and_publish(track: rtc.Track, video_stream: rtc.VideoStream):
    track_sid = track._info.sid
    async with app_state._lock:
        if (track_sid not in app_state.video_writers and app_state.is_recording):
            await app_state.rotate_video_writer(track_sid)
    try:
        async for frame_event in video_stream:
            # Check if we need to rotate to a new chunk
            current_time = time.time()
            if (track_sid in app_state.chunk_start_time and
                current_time - app_state.chunk_start_time[track_sid] >= app_state.chunk_duration):
                await app_state.rotate_video_writer(track_sid)

            # CRITICAL FIX: Check if track still exists and recording is active
            async with app_state._lock:
                should_process = (
                    track_sid in app_state.video_writers and
                    app_state.video_writers[track_sid] is not None and
                    (app_state.pending_task.get(track_sid, 0) > 0 or app_state.is_recording)
                )

            if should_process:
                try:
                    # Thread-safe increment
                    _ = await app_state.safe_increment_pending(track_sid)

                    buffer = frame_event.frame
                    arr = np.frombuffer(buffer.data, dtype=np.uint8)
                    arr = arr.reshape((buffer.height, buffer.width, 3))
                    arr = cv2.cvtColor(arr, cv2.COLOR_BGR2RGB)

                    # Process frame with pose estimation
                    processed_frame = await process_frame_with_pose(arr)

                    # CRITICAL FIX: Check writers still exist before writing
                    async with app_state._lock:
                        if track_sid in app_state.video_writers and app_state.video_writers[track_sid] is not None:
                            if processed_frame is not None:
                                try:
                                    writing_img = cv2.resize(processed_frame, (app_state.video_width, app_state.video_height))
                                    app_state.video_writers[track_sid].write(writing_img)
                                except Exception as e:
                                    LOGGER.error(f"Error writing processed frame for {track_sid}: {str(e)}")

                            if track_sid in app_state.video_origin_writers and app_state.video_origin_writers[track_sid] is not None:
                                try:
                                    writing_img_origin = cv2.resize(arr, (app_state.video_width, app_state.video_height))
                                    app_state.video_origin_writers[track_sid].write(writing_img_origin)
                                except Exception as e:
                                    LOGGER.error(f"Error writing original frame for {track_sid}: {str(e)}")

                except Exception as e:
                    LOGGER.error(f"Error processing frame for {track_sid}: {str(e)}", exc_info=True)
                finally:
                    # Thread-safe decrement
                    _ = await app_state.safe_decrement_pending(track_sid)
                    gc.collect()

            else:
                # If not recording, just skip this frame
                await asyncio.sleep(0.001)  # Small delay to prevent busy waiting

    except Exception as e:
        LOGGER.error(f"Error in process_and_publish for {track_sid}: {str(e)}", exc_info=True)
        # Ensure we don't leave the pending_task counter in an invalid state
        await app_state.safe_reset_pending(track_sid)


async def periodic_cleanup_task():
    """Background task for periodic cleanup"""
    while True:
        try:
            await asyncio.sleep(30)  # Run cleanup every minute
            app_state.periodic_cleanup()
        except asyncio.CancelledError:
            break
        except Exception as e:
            LOGGER.error(f"Error in periodic cleanup: {str(e)}", exc_info=True)

async def main(room: rtc.Room, message, queue_channel, queue_method) -> None:
    @room.on("participant_disconnected")
    def on_participant_disconnected(participant: rtc.RemoteParticipant):
        LOGGER.info(f"PARTICIPANT DISCONECT")
        LOGGER.info(f"nbParticipant = {len(room.remote_participants)}")
        if len(room.remote_participants) == 0:
            stop_server(queue_channel, queue_method)

    @room.on("track_subscribed")
    def on_track_subscribed(track: rtc.Track):
        if track.kind == rtc.TrackKind.KIND_VIDEO:
            LOGGER.info(f"subscribed to track: {track.name}")
            video_stream = rtc.VideoStream(track, format=rtc.VideoBufferType.RGB24)
            # Start processing task
            task = asyncio.create_task(process_and_publish(track=track, video_stream=video_stream))
            app_state.tasks[track._info.sid] = task
            # Track background task to prevent it from being garbage collected
            app_state.background_tasks.add(task)
            task.add_done_callback(app_state.background_tasks.discard)

    @room.on("track_unsubscribed")
    def on_track_unsubscribed(
        track: rtc.Track,
        publication: rtc.RemoteTrackPublication,
        participant: rtc.RemoteParticipant,
    ):
        if track.kind == rtc.TrackKind.KIND_VIDEO:
            track_sid = track._info.sid
            LOGGER.info(f"Track unsubscribed: {track_sid}")

            # Cancel the processing task
            if track_sid in app_state.tasks:
                try:
                    app_state.tasks[track_sid].cancel()
                    app_state.tasks.pop(track_sid, None)
                except Exception as e:
                    LOGGER.error(f"Error cancelling task for {track_sid}: {e}")

            # Clean up all resources for this track asynchronously
            cleanup_task = asyncio.create_task(app_state.cleanup_track_resources(track_sid))
            app_state.background_tasks.add(cleanup_task)
            cleanup_task.add_done_callback(app_state.background_tasks.discard)

    @room.on("data_received")
    def on_data_received(data: rtc.DataPacket):
        LOGGER.info(f"received data from {data.participant._info.sid}: {data.data}")
        if data.data.decode("utf-8") == "START_RECORDING":
            app_state.is_recording = True
            # FIXED: Properly track background tasks
            task = asyncio.create_task(handle_start_recording())
            app_state.background_tasks.add(task)
            task.add_done_callback(app_state.background_tasks.discard)
        elif data.data.decode("utf-8") == "STOP_RECORDING":
            # FIXED: Properly track background tasks
            task = asyncio.create_task(handle_stop_recording(room=room))
            app_state.background_tasks.add(task)
            task.add_done_callback(app_state.background_tasks.discard)
            app_state.is_recording = False


    room_info = json.loads(message)
    o_token = api.AccessToken(LiveKitConfig.apiKey, LiveKitConfig.apiSecret)
    # o_token = api.AccessToken()
    service_identity = str(uuid.uuid4())
    token = (
        o_token.with_identity(service_identity)
        .with_grants(api.VideoGrants(room_create=True, room_join=True, can_publish=True, can_subscribe=True, can_publish_data=True, room_record=True, room_admin=True, room=room_info['roomName'], can_update_own_metadata=True,))
        .to_jwt()
    )
    await room.connect(LiveKitConfig.url, token)
    LOGGER.debug("connected to room: " + room.name)

    # Start periodic cleanup task
    cleanup_task = asyncio.create_task(periodic_cleanup_task())
    app_state.background_tasks.add(cleanup_task)
    cleanup_task.add_done_callback(app_state.background_tasks.discard)

    # Keep the connection alive
    try:
        # Wait indefinitely until the room is disconnected or an error occurs
        await asyncio.Event().wait()
    except Exception as e:
        LOGGER.error(f"Error in main loop: {str(e)}", exc_info=True)
    finally:
        # Clean up when exiting
        cleanup_task.cancel()
        await room.disconnect()

def callback(ch, method, properties, body):
    LOGGER.info(f" [x] Received {body}")
    thread = threading.Thread(target=start_process, args=(ch, body.decode(), method))
    thread.start()

def start_process(queue_channel, message, queue_method):
    # FIXED: Better event loop management
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        room = rtc.Room(loop=loop)
        LOGGER.info(f"Received {message}.")

        # Create main task and run until completion or error
        main_task = loop.create_task(main(room, message, queue_channel, queue_method))

        try:
            loop.run_until_complete(main_task)
        except Exception as e:
            LOGGER.error(f"Error in main task: {str(e)}", exc_info=True)
        finally:
            # Clean up pending tasks
            pending_tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
            if pending_tasks:
                LOGGER.info(f"Cancelling {len(pending_tasks)} pending tasks")
                for task in pending_tasks:
                    task.cancel()
                # Wait for tasks to be cancelled
                loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))

            # Close the loop properly
            loop.close()
    except Exception as e:
        LOGGER.error(f"Error in start_process: {str(e)}", exc_info=True)
    finally:
        # Ensure we acknowledge the message even if there's an error
        try:
            queue_channel.basic_ack(delivery_tag=queue_method.delivery_tag)
        except Exception as e:
            LOGGER.error(f"Error acknowledging message: {str(e)}", exc_info=True)


channel.basic_consume(queue=queue_name, on_message_callback=callback, auto_ack=False)
LOGGER.info(' [*] Waiting for messages. To exit press CTRL+C')
channel.start_consuming()